import os
import time

# 🔒 Set visible CUDA device to GPU 1 (makes it appear as cuda:0)
os.environ["CUDA_VISIBLE_DEVICES"] = "1"
# 🔧 Disable torch.compile (TorchDynamo) entirely
os.environ["TORCH_DISABLE_DYNAMO"] = "1"
os.environ["NO_TORCH_COMPILE"] = "1"  # Also disable Triton compilation

# 🛡️ Suppress TorchDynamo internal fallback crashes

import torch._dynamo
torch._dynamo.config.suppress_errors = True

import torch
import torchaudio
from huggingface_hub import hf_hub_download
from generator import load_csm_1b, Segment
from dataclasses import dataclass

# 🧹 Clear GPU memory before loading model
torch.cuda.empty_cache()
torch.cuda.reset_peak_memory_stats()

# 🚀 Download prompt audios
# prompt_filepath_conversational_a = hf_hub_download(
#     repo_id="sesame/csm-1b",
#     filename="prompts/conversational_a.wav"
# )
# prompt_filepath_conversational_b = hf_hub_download(
#     repo_id="sesame/csm-1b",
#     filename="prompts/conversational_b.wav"
# )
prompt_file_path_conversational_a = "/root/ai_compute/SesameCSM/promo_1.wav"
prompt_file_path_conversational_b = "/root/ai_compute/SesameCSM/promo_1.wav"

# 🗣️ Define speaker prompt content
SPEAKER_PROMPTS = {
    "conversational_a": {
        "text": (
            "What makes you different from others. What makes you truly unique. Its your ambition. It uplifts the soul gives you a reason a reason to rise a reason to excel. And here at Ambition Guru College its all about ambition"
        ),
        "audio": prompt_file_path_conversational_a
    }
}

def load_prompt_audio(audio_path: str, target_sample_rate: int) -> torch.Tensor:
    audio_tensor, sample_rate = torchaudio.load(audio_path)
    audio_tensor = audio_tensor.squeeze(0)
    audio_tensor = torchaudio.functional.resample(
        audio_tensor, orig_freq=sample_rate, new_freq=target_sample_rate
    )
    return audio_tensor

def prepare_prompt(text: str, speaker: int, audio_path: str, sample_rate: int) -> Segment:
    audio_tensor = load_prompt_audio(audio_path, sample_rate)
    return Segment(text=text, speaker=speaker, audio=audio_tensor)

def main():
    # ⏱️ Start total execution timer
    total_start_time = time.time()

    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")

    # ⏱️ Time model loading
    print("Loading CSM model...")
    model_load_start = time.time()
    generator = load_csm_1b(device)
    model_load_time = time.time() - model_load_start
    print(f"✅ Model loaded in {model_load_time:.2f} seconds")

    prompt_a = prepare_prompt(
        SPEAKER_PROMPTS["conversational_a"]["text"],
        0,
        SPEAKER_PROMPTS["conversational_a"]["audio"],
        generator.sample_rate
    )

    conversation = [
        {"text": "Time is the most democratic of all resources. Each person, regardless of wealth, status, or geography, is given the exact same 24 hours in a day. Yet, what separates the successful from the stagnant, the joyful from the anxious, and the wise from the regretful is how they use those hours. The value of time is not merely in its passing — it is in its application.", "speaker_id": 0},
    ]

    generated_segments = []
    prompt_segments = [prompt_a]

    # ⏱️ Time audio generation
    print("\nStarting audio generation...")
    generation_start_time = time.time()

    for i, utterance in enumerate(conversation):
        print(f"\n🎵 Generating utterance {i+1}/{len(conversation)}: {utterance['text'][:50]}...")

        # ⏱️ Time individual utterance generation
        utterance_start = time.time()
        audio_tensor = generator.generate(
            text=utterance['text'],
            speaker=utterance['speaker_id'],
            context=prompt_segments + generated_segments,
            max_audio_length_ms=40_000,
        )
        utterance_time = time.time() - utterance_start

        generated_segments.append(Segment(text=utterance['text'], speaker=utterance['speaker_id'], audio=audio_tensor))
        print(f"✅ Utterance {i+1} generated in {utterance_time:.2f} seconds")

    total_generation_time = time.time() - generation_start_time
    print(f"\n🎯 Total generation time: {total_generation_time:.2f} seconds")

    # ⏱️ Time audio concatenation and saving
    print("\n💾 Saving final audio file...")
    save_start = time.time()
    all_audio = torch.cat([seg.audio for seg in generated_segments], dim=0)
    torchaudio.save(
        "full_conversation.wav",
        all_audio.unsqueeze(0).cpu(),
        generator.sample_rate
    )
    save_time = time.time() - save_start
    print(f"✅ Audio saved in {save_time:.2f} seconds")

    # ⏱️ Calculate and display total execution time
    total_execution_time = time.time() - total_start_time
    print(f"\n🏁 TOTAL EXECUTION TIME: {total_execution_time:.2f} seconds")
    print(f"📊 Breakdown:")
    print(f"   - Model loading: {model_load_time:.2f}s ({model_load_time/total_execution_time*100:.1f}%)")
    print(f"   - Audio generation: {total_generation_time:.2f}s ({total_generation_time/total_execution_time*100:.1f}%)")
    print(f"   - Audio saving: {save_time:.2f}s ({save_time/total_execution_time*100:.1f}%)")
    print("Successfully generated full_conversation.wav")

if __name__ == "__main__":
    main()
